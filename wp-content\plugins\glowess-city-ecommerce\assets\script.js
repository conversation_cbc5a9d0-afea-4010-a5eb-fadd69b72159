/**
 * Glowess City Based E-Commerce - Frontend JavaScript
 * Compatible with jQuery and WordPress
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Global variables
    var cityModal = $('#city-selector-modal');
    var selectedCity = getCookie('selected_city_id');
    
    // Initialize plugin
    init();
    
    function init() {
        // Show city modal if no city selected
        if (!selectedCity && cityModal.length) {
            showCityModal();
        }
        
        // Bind events
        bindEvents();
        
        // Initialize components
        initCitySelector();
    }
    
    function bindEvents() {
        // City selection in modal
        $(document).on('click', '.city-option', handleCitySelection);
        
        // City selector dropdown
        $(document).on('click', '.current-city-display', toggleCityDropdown);
        $(document).on('click', '.city-dropdown-item', handleDropdownCitySelection);
        

        // Close modal on outside click
        $(document).on('click', '.city-modal-overlay', function(e) {
            if (e.target === this) {
                closeCityModal();
            }
        });
        
        // Close dropdown on outside click
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.glowess-city-selector').length) {
                $('.glowess-city-selector').removeClass('open');
                $('.city-dropdown').hide();
            }
        });
        
        // Smooth scroll for categories
        $(document).on('click', 'a[href^="#category-"]', function(e) {
            e.preventDefault();
            var target = $(this.getAttribute('href'));
            if (target.length) {
                smoothScrollTo(target);
            }
        });
    }
    
    function showCityModal() {
        cityModal.fadeIn(300);
        $('body').addClass('modal-open').css('overflow', 'hidden');
    }
    
    function closeCityModal() {
        cityModal.fadeOut(300);
        $('body').removeClass('modal-open').css('overflow', '');
    }
    
    function handleCitySelection(e) {
        e.preventDefault();
        
        var cityId = $(this).data('city-id');
        var citySlug = $(this).data('city-slug');
        
        if (!cityId) return;
        
        // Show loading
        $(this).addClass('loading');
        
        // AJAX call to save city selection
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'select_city',
                city_id: cityId,
                city_slug: citySlug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Set cookies
                    setCookie('selected_city_id', cityId, 30);
                    setCookie('selected_city_slug', citySlug, 30);
                    
                    // Close modal
                    closeCityModal();
                    
                    // Reload page to show city-specific content
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showError('Şehir seçimi kaydedilemedi. Lütfen tekrar deneyin.');
                }
            },
            error: function() {
                showError('Bir hata oluştu. Lütfen tekrar deneyin.');
            },
            complete: function() {
                $('.city-option').removeClass('loading');
            }
        });
    }
    
    function toggleCityDropdown(e) {
        e.preventDefault();
        e.stopPropagation();
        
        var selector = $(this).closest('.glowess-city-selector');
        var dropdown = selector.find('.city-dropdown');
        
        selector.toggleClass('open');
        dropdown.toggle();
    }
    
    function handleDropdownCitySelection(e) {
        e.preventDefault();
        
        var cityId = $(this).data('city-id');
        var citySlug = $(this).data('city-slug');
        var cityName = $(this).text();
        
        if (!cityId) return;
        
        // Update display
        $(this).closest('.glowess-city-selector').find('.city-name').text(cityName);
        
        // Close dropdown
        $('.glowess-city-selector').removeClass('open');
        $('.city-dropdown').hide();
        
        // Save selection and reload
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'select_city',
                city_id: cityId,
                city_slug: citySlug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    setCookie('selected_city_id', cityId, 30);
                    setCookie('selected_city_slug', citySlug, 30);
                    location.reload();
                }
            }
        });
    }
    
    function initCitySelector() {
        // Initialize any existing city selectors
        $('.glowess-city-selector').each(function() {
            var selector = $(this);
            // Additional initialization if needed
        });
    }
    


    // Search functionality
    function initCitySearch() {
        var searchInput = $('#city-search');
        if (searchInput.length) {
            searchInput.on('input', function() {
                var query = $(this).val().toLowerCase();
                var cities = $('.city-option');
                
                cities.each(function() {
                    var cityName = $(this).find('span').text().toLowerCase();
                    if (cityName.includes(query)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        }
    }
    
    // Initialize search
    initCitySearch();
    
    
    // Performance optimization
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
    
    // Image lazy loading fallback
    function lazyLoadImages() {
        $('.lazy').each(function() {
            var img = $(this);
            var src = img.data('src');
            
            if (src && isElementInViewport(img[0])) {
                img.attr('src', src).removeClass('lazy');
            }
        });
    }
    
    function isElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // Initialize lazy loading fallback
    $(window).on('scroll resize', debounce(lazyLoadImages, 100));
    lazyLoadImages();

    
    // Console welcome message
    console.log('%cGlowess City Plugin', 'color: #333; font-size: 16px; font-weight: bold;');
    console.log('%cŞehir bazlı e-ticaret sistemi aktif.', 'color: #666; font-size: 12px;');
    

});

// jQuery dışında çalışacak fonksiyonlar
(function() {
    'use strict';
    
    // Page visibility API
    var hidden = "hidden";
    var visibilityChange = "visibilitychange";
    
    if (typeof document.hidden !== "undefined") {
        hidden = "hidden";
        visibilityChange = "visibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
        hidden = "msHidden";
        visibilityChange = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
        hidden = "webkitHidden";
        visibilityChange = "webkitvisibilitychange";
    }
    
    function handleVisibilityChange() {
        if (document[hidden]) {
            // Page is hidden
        } else {
            // Page is visible
        }
    }
    
    if (typeof document.addEventListener !== "undefined" && hidden !== undefined) {
        document.addEventListener(visibilityChange, handleVisibilityChange, false);
    }
    
    // Service Worker registration (for future PWA features)
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            // navigator.registerServiceWorker('/sw.js') - future implementation
        });
    }
    
    
    // Load non-critical CSS
    setTimeout(function() {
        // loadCSS('/path/to/non-critical.css');
    }, 100);
    
})();