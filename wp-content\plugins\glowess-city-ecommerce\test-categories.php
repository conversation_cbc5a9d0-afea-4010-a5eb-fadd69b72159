<?php
/**
 * Test dosyası - Kategori filtreleme testi
 * Bu dosyayı tarayıcıda açarak kategori filtrelemenin çalışıp çalışmadığını test edebilirsiniz
 * URL: yoursite.com/wp-content/plugins/glowess-city-ecommerce/test-categories.php
 */

// WordPress'i yükle
require_once('../../../wp-load.php');

// Güvenlik kontrolü
if (!current_user_can('manage_options')) {
    die('Bu sayfaya erişim yetkiniz yok.');
}

echo '<h1>Glowess City - Kategori Filtreleme Testi</h1>';

// Seçili şehri al
$selected_city = glowess_get_selected_city();
echo '<h2>Seçili Şehir: ' . ($selected_city ? get_post($selected_city)->post_title . ' (ID: ' . $selected_city . ')' : 'Seçili şehir yok') . '</h2>';

if (!$selected_city) {
    echo '<p style="color: red;"><strong>Uyarı:</strong> Şehir seçilmemiş. Önce bir şehir seçin.</p>';
    echo '<p><a href="' . home_url() . '">Ana sayfaya git ve şehir seç</a></p>';
    exit;
}

echo '<h2>Tüm Kategoriler ve Şehir Atamaları:</h2>';

// Tüm kategorileri al
$all_categories = get_terms(array(
    'taxonomy' => 'product_cat',
    'hide_empty' => false,
));

echo '<table border="1" cellpadding="5" cellspacing="0">';
echo '<tr><th>Kategori Adı</th><th>ID</th><th>Atanmış Şehirler</th><th>Seçili Şehirde Görünmeli mi?</th></tr>';

foreach ($all_categories as $category) {
    $available_cities = get_term_meta($category->term_id, '_available_cities', true);
    
    $should_be_visible = false;
    if (empty($available_cities)) {
        $should_be_visible = true; // Hiç şehir atanmamışsa tüm şehirlerde görünür
        $cities_text = 'Tüm şehirler (boş)';
    } elseif (is_array($available_cities) && in_array($selected_city, $available_cities)) {
        $should_be_visible = true;
        $city_names = array();
        foreach ($available_cities as $city_id) {
            $city_post = get_post($city_id);
            $city_names[] = $city_post ? $city_post->post_title : 'Bilinmeyen şehir';
        }
        $cities_text = implode(', ', $city_names);
    } else {
        $cities_text = 'Diğer şehirler';
    }
    
    $visibility_color = $should_be_visible ? 'green' : 'red';
    $visibility_text = $should_be_visible ? 'EVET' : 'HAYIR';
    
    echo '<tr>';
    echo '<td>' . esc_html($category->name) . '</td>';
    echo '<td>' . $category->term_id . '</td>';
    echo '<td>' . esc_html($cities_text) . '</td>';
    echo '<td style="color: ' . $visibility_color . '; font-weight: bold;">' . $visibility_text . '</td>';
    echo '</tr>';
}

echo '</table>';

echo '<h2>Filtrelenmiş Kategoriler (get_terms ile):</h2>';

// Filtrelenmiş kategorileri al
$filtered_categories = get_terms(array(
    'taxonomy' => 'product_cat',
    'hide_empty' => false,
    'meta_query' => array(
        'relation' => 'OR',
        array(
            'key' => '_available_cities',
            'value' => sprintf('i:%d;', $selected_city),
            'compare' => 'LIKE'
        ),
        array(
            'key' => '_available_cities',
            'compare' => 'NOT EXISTS'
        ),
        array(
            'key' => '_available_cities',
            'value' => '',
            'compare' => '='
        )
    )
));

if (empty($filtered_categories)) {
    echo '<p style="color: red;">Hiç kategori bulunamadı! Bu filtreleme sorunu olduğunu gösterir.</p>';
} else {
    echo '<ul>';
    foreach ($filtered_categories as $category) {
        echo '<li>' . esc_html($category->name) . ' (ID: ' . $category->term_id . ')</li>';
    }
    echo '</ul>';
}

echo '<h2>Debug Bilgileri:</h2>';
echo '<p><strong>Arama Deseni 1:</strong> <code>' . sprintf('i:%d;', $selected_city) . '</code></p>';
echo '<p><strong>Arama Deseni 2:</strong> <code>' . sprintf('"i:%d;"', $selected_city) . '</code></p>';

// Örnek bir kategorinin meta değerini göster
if (!empty($all_categories)) {
    $sample_category = $all_categories[0];
    $sample_meta = get_term_meta($sample_category->term_id, '_available_cities', true);
    echo '<p><strong>Örnek kategori meta değeri:</strong></p>';
    echo '<pre>' . print_r($sample_meta, true) . '</pre>';
    if (is_array($sample_meta)) {
        echo '<p><strong>Serialize edilmiş hali:</strong> <code>' . serialize($sample_meta) . '</code></p>';
    }
}

echo '<hr>';
echo '<p><a href="' . admin_url('edit-tags.php?taxonomy=product_cat&post_type=product') . '">Kategorileri düzenle</a></p>';
echo '<p><a href="' . home_url() . '">Ana sayfaya dön</a></p>';
?>
