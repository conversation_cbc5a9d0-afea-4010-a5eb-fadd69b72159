<?php
/**
 * Plugin Name: Glowess City Based E-Commerce
 * Plugin URI: https://sewpos.com
 * Description: Şehir bazlı e-ticaret sistemi - Glowess teması ve WooCommerce uyumlu
 * Version: 1.0.0
 * Author: <PERSON><PERSON>ez
 * License: GPL v2 or later
 * Text Domain: glowess-city
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.8.2
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 9.0
 */

// Güvenlik kontrolü
if (!defined('ABSPATH')) {
    exit;
}

// Plugin sürümü ve yolu
define('GLOWESS_CITY_VERSION', '1.0.0');
define('GLOWESS_CITY_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GLOWESS_CITY_PLUGIN_PATH', plugin_dir_path(__FILE__));

class GlowessCityEcommerce {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // WooCommerce kontrolü
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }
        
        // Custom Post Type'ı burada direkt çağır - DÜZELTME
        $this->create_cities_post_type();
        
        // Hook'ları başlat
        $this->init_hooks();
        
        // Admin panel
        if (is_admin()) {
            $this->init_admin();
        }
        
        // Frontend
        $this->init_frontend();
    }
    
    private function init_hooks() {
        // Custom Post Type hook'unu KALDIR - DÜZELTME
        // add_action('init', array($this, 'create_cities_post_type')); // BU SATIRI SİLDİK
        
        // Meta boxes
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        add_action('save_post', array($this, 'save_meta_boxes'));
        
        // AJAX handlers
        add_action('wp_ajax_select_city', array($this, 'handle_city_selection'));
        add_action('wp_ajax_nopriv_select_city', array($this, 'handle_city_selection'));
        
        // WooCommerce hooks
        add_action('woocommerce_product_options_general_product_data', array($this, 'add_city_fields_to_product'));
        add_action('woocommerce_process_product_meta', array($this, 'save_product_city_fields'));

        // WooCommerce Category hooks - Kategori şehir seçimi için
        add_action('product_cat_add_form_fields', array($this, 'add_city_fields_to_category_add'));
        add_action('product_cat_edit_form_fields', array($this, 'add_city_fields_to_category_edit'));
        add_action('edited_product_cat', array($this, 'save_category_city_fields'));
        add_action('create_product_cat', array($this, 'save_category_city_fields'));

        // Kategori filtreleme
        add_action('pre_get_posts', array($this, 'filter_categories_by_city'), 5);

        // WooCommerce kategori filtreleme (Gutenberg blokları için)
        add_filter('get_terms_args', array($this, 'filter_woocommerce_category_terms'), 10, 2);
        add_filter('terms_clauses', array($this, 'filter_category_terms_clauses'), 10, 3);

        // WooCommerce widget'ları için kategori filtreleme
        add_filter('woocommerce_product_categories_widget_args', array($this, 'filter_category_widget_args'));
        add_filter('widget_product_categories_args', array($this, 'filter_category_widget_args'));
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'city_selector_modal'));
        
        // Query modification
        add_action('pre_get_posts', array($this, 'filter_products_by_city'));

        // WooCommerce specific query modification (for Gutenberg blocks)
        add_filter('woocommerce_product_query_meta_query', array($this, 'filter_woocommerce_product_query'), 10, 2);

        // Alternative method for Gutenberg blocks - modify all product queries
        add_action('pre_get_posts', array($this, 'filter_all_product_queries'), 20);
        
        // Shortcodes
        add_shortcode('city_selector', array($this, 'city_selector_shortcode'));
    }
    
    private function init_admin() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
    }
    
    private function init_frontend() {

        // Block theme için hook'lar
        add_action('wp_enqueue_scripts', array($this, 'enqueue_block_theme_styles'));
    }
    
    public function create_cities_post_type() {
        $args = array(
            'label' => 'Şehirler',
            'public' => true,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => true,
            'query_var' => true,
            'rewrite' => false,
            'capability_type' => 'post',
            'has_archive' => false,
            'hierarchical' => false,
            'menu_position' => 25,
            'menu_icon' => 'dashicons-location-alt',
            'supports' => array('title', 'editor', 'thumbnail'),
            'labels' => array(
                'name' => 'Şehirler',
                'singular_name' => 'Şehir',
                'add_new' => 'Yeni Şehir Ekle',
                'add_new_item' => 'Yeni Şehir Ekle',
                'edit_item' => 'Şehir Düzenle',
                'new_item' => 'Yeni Şehir',
                'view_item' => 'Şehri Görüntüle',
                'search_items' => 'Şehir Ara',
                'not_found' => 'Şehir bulunamadı',
                'not_found_in_trash' => 'Çöp kutusunda şehir bulunamadı'
            )
        );
        
        return register_post_type('cities', $args);
    }
    
    public function add_meta_boxes() {
        // Şehir meta boxes
        add_meta_box(
            'city_details',
            'Şehir Detayları',
            array($this, 'city_details_meta_box'),
            'cities',
            'normal',
            'high'
        );
    }
    
    public function city_details_meta_box($post) {
        wp_nonce_field('city_details_nonce', 'city_details_nonce');
        
        $hero_image = get_post_meta($post->ID, '_city_hero_image', true);
        $delivery_areas = get_post_meta($post->ID, '_city_delivery_areas', true);
        $delivery_times = get_post_meta($post->ID, '_city_delivery_times', true);
        $is_active = get_post_meta($post->ID, '_city_is_active', true);
        $city_slug = get_post_meta($post->ID, '_city_slug', true);
        
        ?>
        <table class="form-table">
            <tr>
                <th><label for="city_hero_image">Hero Görseli URL</label></th>
                <td>
                    <input type="url" id="city_hero_image" name="city_hero_image" value="<?php echo esc_attr($hero_image); ?>" class="regular-text" />
                    <p class="description">Ana sayfa büyük görsel URL'si</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_slug">Şehir Kodu</label></th>
                <td>
                    <input type="text" id="city_slug" name="city_slug" value="<?php echo esc_attr($city_slug); ?>" class="regular-text" />
                    <p class="description">URL için kullanılacak (örn: istanbul, ankara)</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_delivery_areas">Teslimat Alanları</label></th>
                <td>
                    <textarea id="city_delivery_areas" name="city_delivery_areas" rows="4" cols="50"><?php echo esc_textarea($delivery_areas); ?></textarea>
                    <p class="description">Her satıra bir ilçe yazın</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_delivery_times">Teslimat Süreleri</label></th>
                <td>
                    <textarea id="city_delivery_times" name="city_delivery_times" rows="4" cols="50"><?php echo esc_textarea($delivery_times); ?></textarea>
                    <p class="description">İlçe: Teslimat süresi formatında</p>
                </td>
            </tr>
            <tr>
                <th><label for="city_is_active">Durum</label></th>
                <td>
                    <label>
                        <input type="checkbox" id="city_is_active" name="city_is_active" value="1" <?php checked($is_active, '1'); ?> />
                        Aktif
                    </label>
                </td>
            </tr>
        </table>
        <?php
    }
    
    public function save_meta_boxes($post_id) {
        if (!isset($_POST['city_details_nonce']) || !wp_verify_nonce($_POST['city_details_nonce'], 'city_details_nonce')) {
            return;
        }
        
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
        
        $fields = array('city_hero_image', 'city_slug', 'city_delivery_areas', 'city_delivery_times', 'city_is_active');
        
        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }
    
    public function add_city_fields_to_product() {
        global $woocommerce, $post;
        
        echo '<div class="product_city_fields show_if_simple show_if_variable">';
        
        // Şehirleri getir
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));
        
        $selected_cities = get_post_meta($post->ID, '_available_cities', true);
        if (!is_array($selected_cities)) {
            $selected_cities = array();
        }
        
        echo '<p class="form-field"><label><strong>Bu ürün hangi şehirlerde satılacak?</strong></label></p>';
        
        foreach ($cities as $city) {
            $checked = in_array($city->ID, $selected_cities) ? 'checked' : '';
            echo '<p class="form-field">';
            echo '<label><input type="checkbox" name="available_cities[]" value="' . $city->ID . '" ' . $checked . '> ' . $city->post_title . '</label>';
            echo '</p>';
        }
        
        echo '</div>';
    }
    
    public function save_product_city_fields($post_id) {
        $available_cities = isset($_POST['available_cities']) ? $_POST['available_cities'] : array();
        update_post_meta($post_id, '_available_cities', $available_cities);
    }
    
    public function enqueue_scripts() {
        wp_enqueue_script('glowess-city-js', GLOWESS_CITY_PLUGIN_URL . 'assets/script.js', array('jquery'), GLOWESS_CITY_VERSION, true);
        wp_enqueue_style('glowess-city-css', GLOWESS_CITY_PLUGIN_URL . 'assets/style.css', array(), GLOWESS_CITY_VERSION);
        
        wp_localize_script('glowess-city-js', 'glowess_city_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('glowess_city_nonce')
        ));
    }
    
    public function city_selector_modal() {
        $selected_city = $this->get_selected_city();
        
        if (!$selected_city) {
            $cities = get_posts(array(
                'post_type' => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_city_is_active',
                        'value' => '1',
                        'compare' => '='
                    )
                )
            ));
            
            if (!empty($cities)) {
                ?>
                <div id="city-selector-modal" class="city-modal-overlay">
                    <div class="city-modal">
                        <div class="city-modal-header">
                            <h2>Hangi şehirdesiniz?</h2>
                            <p>Size en uygun ürünleri gösterebilmek için şehrinizi seçin</p>
                        </div>
                        <div class="city-grid">
                            <?php foreach ($cities as $city): 
                                $city_image = get_post_meta($city->ID, '_city_hero_image', true);
                                ?>
                                <div class="city-option" data-city-id="<?php echo $city->ID; ?>" data-city-slug="<?php echo get_post_meta($city->ID, '_city_slug', true); ?>">
                                    <?php if ($city_image): ?>
                                        <img src="<?php echo esc_url($city_image); ?>" alt="<?php echo esc_attr($city->post_title); ?>">
                                    <?php endif; ?>
                                    <span><?php echo esc_html($city->post_title); ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php
            }
        }
    }
    
    public function handle_city_selection() {
        check_ajax_referer('glowess_city_nonce', 'nonce');
        
        $city_id = intval($_POST['city_id']);
        $city_slug = sanitize_text_field($_POST['city_slug']);
        
        if ($city_id > 0) {
            setcookie('selected_city_id', $city_id, time() + (86400 * 30), '/');
            setcookie('selected_city_slug', $city_slug, time() + (86400 * 30), '/');
            
            wp_send_json_success(array(
                'message' => 'Şehir seçimi kaydedildi',
                'city_id' => $city_id,
                'city_slug' => $city_slug
            ));
        } else {
            wp_send_json_error('Geçersiz şehir');
        }
    }
    
    public function get_selected_city() {
        if (isset($_COOKIE['selected_city_id'])) {
            return intval($_COOKIE['selected_city_id']);
        }
        return false;
    }
    
    public function get_selected_city_slug() {
        if (isset($_COOKIE['selected_city_slug'])) {
            return sanitize_text_field($_COOKIE['selected_city_slug']);
        }
        return false;
    }
    
    public function filter_products_by_city($query) {
        if (!is_admin() && $query->is_main_query()) {
            $selected_city = $this->get_selected_city();

            if ($selected_city && (is_shop() || is_product_category() || is_home())) {
                $meta_query = $query->get('meta_query');
                if (!is_array($meta_query)) {
                    $meta_query = array();
                }

                $meta_query[] = array(
                    'key' => '_available_cities',
                    'value' => serialize(strval($selected_city)),
                    'compare' => 'LIKE'
                );

                $query->set('meta_query', $meta_query);
            }
        }
    }

    /**
     * WooCommerce ürün sorgularını şehir bazlı filtrele (Gutenberg blokları için)
     * Bu fonksiyon tüm WooCommerce ürün sorgularını etkiler (ana sorgu olmasalar bile)
     */
    public function filter_woocommerce_product_query($meta_query, $query) {
        // Admin panelinde çalışmasın
        if (is_admin()) {
            return $meta_query;
        }

        $selected_city = $this->get_selected_city();

        // Şehir seçilmişse ve ürün sorgusu ise filtre uygula
        if ($selected_city && isset($query->query_vars['post_type']) && $query->query_vars['post_type'] === 'product') {
            if (!is_array($meta_query)) {
                $meta_query = array();
            }

            $meta_query[] = array(
                'key' => '_available_cities',
                'value' => serialize(strval($selected_city)),
                'compare' => 'LIKE'
            );
        }

        return $meta_query;
    }

    /**
     * Tüm ürün sorgularını şehir bazlı filtrele (Gutenberg blokları için alternatif yöntem)
     * Bu fonksiyon ana sorgu kontrolü yapmadan tüm ürün sorgularını etkiler
     */
    public function filter_all_product_queries($query) {
        // Admin panelinde çalışmasın
        if (is_admin()) {
            return;
        }

        // Sadece ürün sorguları için çalış
        if (!isset($query->query_vars['post_type']) || $query->query_vars['post_type'] !== 'product') {
            return;
        }

        $selected_city = $this->get_selected_city();

        if ($selected_city) {
            $meta_query = $query->get('meta_query');
            if (!is_array($meta_query)) {
                $meta_query = array();
            }

            // Şehir filtresi zaten eklenmiş mi kontrol et
            $city_filter_exists = false;
            foreach ($meta_query as $meta_condition) {
                if (isset($meta_condition['key']) && $meta_condition['key'] === '_available_cities') {
                    $city_filter_exists = true;
                    break;
                }
            }

            // Şehir filtresi yoksa ekle
            if (!$city_filter_exists) {
                $meta_query[] = array(
                    'key' => '_available_cities',
                    'value' => serialize(strval($selected_city)),
                    'compare' => 'LIKE'
                );

                $query->set('meta_query', $meta_query);
            }
        }
    }

    /**
     * Kategorileri şehir bazlı filtrele
     * Seçilen şehre göre kategorileri gizle/göster
     */
    public function filter_categories_by_city($query) {
        // Admin panelinde çalışmasın
        if (is_admin()) {
            return;
        }

        // Sadece kategori sorguları için çalış
        if (!isset($query->query_vars['taxonomy']) || $query->query_vars['taxonomy'] !== 'product_cat') {
            return;
        }

        $selected_city = $this->get_selected_city();
        if (!$selected_city) {
            return;
        }

        // Mevcut meta query'yi al
        $meta_query = $query->get('meta_query');
        if (!is_array($meta_query)) {
            $meta_query = array();
        }

        // DÜZELTME: Şehir filtresi ekle - kategorinin şehir listesinde seçili şehir var mı kontrol et
        $meta_query[] = array(
            'relation' => 'OR',
            array(
                'key' => '_available_cities',
                'value' => sprintf('i:%d;', $selected_city), // Array içinde integer değeri ara
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_available_cities',
                'compare' => 'NOT EXISTS' // Hiç şehir seçilmemişse tüm şehirlerde göster
            ),
            array(
                'key' => '_available_cities',
                'value' => '',
                'compare' => '=' // Boş değer de tüm şehirlerde göster anlamına gelir
            )
        );

        $query->set('meta_query', $meta_query);
    }

    /**
     * WooCommerce kategori terimlerini şehir bazlı filtrele (Gutenberg blokları için)
     */
    public function filter_woocommerce_category_terms($args, $taxonomies) {
        // Admin panelinde çalışmasın
        if (is_admin()) {
            return $args;
        }

        // Sadece product_cat taxonomy'si için çalış
        if (!in_array('product_cat', $taxonomies)) {
            return $args;
        }

        $selected_city = $this->get_selected_city();
        if (!$selected_city) {
            return $args;
        }



        // Meta query ekle
        if (!isset($args['meta_query']) || !is_array($args['meta_query'])) {
            $args['meta_query'] = array();
        }

        // Şehir filtresini ekle - DÜZELTME: Array içinde integer değeri ara
        $city_filter = array(
            'relation' => 'OR',
            array(
                'key' => '_available_cities',
                'value' => sprintf('i:%d;', $selected_city), // Array içinde integer değeri ara
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_available_cities',
                'compare' => 'NOT EXISTS'
            ),
            array(
                'key' => '_available_cities',
                'value' => '',
                'compare' => '='
            )
        );

        // Eğer zaten meta_query varsa, relation ekle
        if (!empty($args['meta_query'])) {
            $args['meta_query'] = array(
                'relation' => 'AND',
                $args['meta_query'],
                $city_filter
            );
        } else {
            $args['meta_query'] = $city_filter;
        }



        return $args;
    }

    /**
     * Kategori terms clauses'ını şehir bazlı filtrele (daha güçlü filtreleme)
     */
    public function filter_category_terms_clauses($clauses, $taxonomies, $args) {
        global $wpdb;

        // Admin panelinde çalışmasın
        if (is_admin()) {
            return $clauses;
        }

        // Sadece product_cat taxonomy'si için çalış
        if (!in_array('product_cat', $taxonomies)) {
            return $clauses;
        }

        $selected_city = $this->get_selected_city();
        if (!$selected_city) {
            return $clauses;
        }

        // Meta join ve where clause'ları ekle
        $clauses['join'] .= " LEFT JOIN {$wpdb->termmeta} AS tm_city ON (t.term_id = tm_city.term_id AND tm_city.meta_key = '_available_cities')";

        // DÜZELTME: Array içinde integer değeri ara
        $city_pattern = sprintf('i:%d;', $selected_city); // i:939; formatı

        $clauses['where'] .= $wpdb->prepare(
            " AND (tm_city.meta_value LIKE %s OR tm_city.meta_value IS NULL OR tm_city.meta_value = '')",
            '%' . $city_pattern . '%'
        );

        return $clauses;
    }

    /**
     * WooCommerce kategori widget'larını şehir bazlı filtrele
     */
    public function filter_category_widget_args($args) {
        // Admin panelinde çalışmasın
        if (is_admin()) {
            return $args;
        }

        $selected_city = $this->get_selected_city();
        if (!$selected_city) {
            return $args;
        }

        // Meta query ekle
        if (!isset($args['meta_query']) || !is_array($args['meta_query'])) {
            $args['meta_query'] = array();
        }

        // DÜZELTME: Array içinde integer değeri ara
        $args['meta_query'][] = array(
            'relation' => 'OR',
            array(
                'key' => '_available_cities',
                'value' => sprintf('i:%d;', $selected_city), // Array içinde integer değeri ara
                'compare' => 'LIKE'
            ),
            array(
                'key' => '_available_cities',
                'compare' => 'NOT EXISTS'
            ),
            array(
                'key' => '_available_cities',
                'value' => '',
                'compare' => '='
            )
        );

        return $args;
    }

    public function city_selector_shortcode($atts) {
        $atts = shortcode_atts(array(
            'style' => 'dropdown'
        ), $atts);
        
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_city_is_active',
                    'value' => '1',
                    'compare' => '='
                )
            )
        ));
        
        $selected_city = $this->get_selected_city();
        $current_city_name = '';
        
        if ($selected_city) {
            $current_city = get_post($selected_city);
            $current_city_name = $current_city ? $current_city->post_title : 'Şehir Seç';
        } else {
            $current_city_name = 'Şehir Seç';
        }
        
        ob_start();
        ?>
        <div class="glowess-city-selector">
            <div class="current-city-display">
                <span class="city-name"><?php echo esc_html($current_city_name); ?></span>
                <span class="dropdown-arrow">▼</span>
            </div>
            <div class="city-dropdown" style="display: none;">
                <?php foreach ($cities as $city): ?>
                    <div class="city-dropdown-item" data-city-id="<?php echo $city->ID; ?>" data-city-slug="<?php echo get_post_meta($city->ID, '_city_slug', true); ?>">
                        <?php echo esc_html($city->post_title); ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Glowess Şehir Ayarları',
            'Şehir Ayarları',
            'manage_options',
            'glowess-city-settings',
            array($this, 'admin_page'),
            'dashicons-location-alt',
            30
        );
    }
    
    public function admin_page() {
        if (isset($_POST['submit'])) {
            update_option('glowess_city_cart_threshold', sanitize_text_field($_POST['cart_threshold']));
            update_option('glowess_city_discount_percentage', sanitize_text_field($_POST['discount_percentage']));
            update_option('glowess_city_info_text', wp_kses_post($_POST['info_text']));
            echo '<div class="notice notice-success"><p>Ayarlar kaydedildi!</p></div>';
        }
        
        $cart_threshold = get_option('glowess_city_cart_threshold', 500);
        $discount_percentage = get_option('glowess_city_discount_percentage', 10);
        $info_text = get_option('glowess_city_info_text', 'X ₺ üzeri alışverişlerde sepetinize otomatik %Y indirim uygulanır.');
        ?>
        <div class="wrap">
            <h1>Glowess Şehir Ayarları</h1>
            <form method="post" action="">
                <table class="form-table">
                    <tr>
                        <th scope="row">Sepet Eşik Tutarı (₺)</th>
                        <td>
                            <input type="number" name="cart_threshold" value="<?php echo esc_attr($cart_threshold); ?>" class="regular-text" />
                            <p class="description">Bu tutarın üzerinde otomatik indirim uygulanacak</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">İndirim Yüzdesi (%)</th>
                        <td>
                            <input type="number" name="discount_percentage" value="<?php echo esc_attr($discount_percentage); ?>" min="1" max="50" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Bilgi Metni</th>
                        <td>
                            <textarea name="info_text" rows="3" cols="50" class="large-text"><?php echo esc_textarea($info_text); ?></textarea>
                            <p class="description">Sepet sayfasında gösterilecek bilgi metni</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
            
            <h2>Aktif Şehirler</h2>
            <?php
            $cities = get_posts(array(
                'post_type' => 'cities',
                'numberposts' => -1,
                'post_status' => 'publish'
            ));
            
            if (!empty($cities)) {
                echo '<table class="wp-list-table widefat fixed striped">';
                echo '<thead><tr><th>Şehir Adı</th><th>Durum</th><th>Ürün Sayısı</th><th>İşlemler</th></tr></thead>';
                echo '<tbody>';
                
                foreach ($cities as $city) {
                    $is_active = get_post_meta($city->ID, '_city_is_active', true);
                    $status = $is_active ? '<span style="color: green;">Aktif</span>' : '<span style="color: red;">Pasif</span>';
                    
                    // Bu şehirdeki ürün sayısını bul
                    $product_count = $this->get_city_product_count($city->ID);
                    
                    echo '<tr>';
                    echo '<td><strong>' . esc_html($city->post_title) . '</strong></td>';
                    echo '<td>' . $status . '</td>';
                    echo '<td>' . $product_count . ' ürün</td>';
                    echo '<td><a href="' . get_edit_post_link($city->ID) . '">Düzenle</a></td>';
                    echo '</tr>';
                }
                
                echo '</tbody></table>';
            } else {
                echo '<p>Henüz şehir eklenmemiş. <a href="' . admin_url('post-new.php?post_type=cities') . '">İlk şehri ekle</a></p>';
            }
            ?>
        </div>
        <?php
    }
    
    private function get_city_product_count($city_id) {
        $args = array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_available_cities',
                    'value' => serialize(strval($city_id)),
                    'compare' => 'LIKE'
                )
            ),
            'fields' => 'ids'
        );
        
        $products = new WP_Query($args);
        return $products->found_posts;
    }
    
    public function admin_enqueue_scripts($hook) {
        if ('toplevel_page_glowess-city-settings' === $hook) {
            wp_enqueue_style('glowess-city-admin-css', GLOWESS_CITY_PLUGIN_URL . 'assets/admin.css', array(), GLOWESS_CITY_VERSION);
        }
    }
    
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><strong>Glowess City Based E-Commerce:</strong> Bu eklenti çalışması için WooCommerce eklentisinin yüklü ve aktif olması gerekmektedir.</p>
        </div>
        <?php
    }
    
    public function load_textdomain() {
        load_plugin_textdomain('glowess-city', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }
    
    public function activate() {
        // Activation işlemleri
        $this->create_cities_post_type();
        flush_rewrite_rules();
        
        // Varsayılan ayarları oluştur
        add_option('glowess_city_cart_threshold', 500);
        add_option('glowess_city_discount_percentage', 10);
        add_option('glowess_city_info_text', 'X ₺ üzeri alışverişlerde sepetinize otomatik %Y indirim uygulanır.');
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }

    public function enqueue_block_theme_styles() {
        wp_add_inline_style('wp-block-library', '
            .glowess-city-info-bar {
                background: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                text-align: center;
                border-left: 4px solid #1976d2;
            }

            .glowess-city-info-bar a {
                color: #1976d2;
                text-decoration: underline;
                margin-left: 10px;
            }

            .glowess-city-availability-notice {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }

            .glowess-city-delivery-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin: 15px 0;
                border-left: 4px solid #28a745;
            }

            .glowess-no-city-selected {
                background: #fff3e0;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                margin: 20px 0;
                border-left: 4px solid #ff9800;
            }

            .glowess-city-btn {
                display: inline-block;
                padding: 10px 20px;
                background: #007cba;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                transition: background 0.3s;
                margin-top: 10px;
            }

            .glowess-city-btn:hover {
                background: #005a87;
                color: white;
            }
        ');
    }



    // Kategori şehir seçimi fonksiyonları
    public function add_city_fields_to_category_add() {
        // Şehirleri getir
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));

        if (empty($cities)) {
            echo '<div class="form-field">';
            echo '<p><strong>Uyarı:</strong> Önce şehir eklemeniz gerekiyor. <a href="' . admin_url('edit.php?post_type=cities') . '">Şehir eklemek için tıklayın</a></p>';
            echo '</div>';
            return;
        }

        echo '<div class="form-field">';
        echo '<label for="available_cities"><strong>Bu kategori hangi şehirlerde gösterilecek?</strong></label>';
        echo '<p class="description">Kategorinin hangi şehirlerde görüneceğini seçin. Hiçbiri seçilmezse tüm şehirlerde görünür.</p>';

        foreach ($cities as $city) {
            echo '<p>';
            echo '<label><input type="checkbox" name="available_cities[]" value="' . $city->ID . '"> ' . esc_html($city->post_title) . '</label>';
            echo '</p>';
        }

        echo '</div>';
    }

    public function add_city_fields_to_category_edit($term) {
        // Şehirleri getir
        $cities = get_posts(array(
            'post_type' => 'cities',
            'numberposts' => -1,
            'post_status' => 'publish'
        ));

        if (empty($cities)) {
            echo '<tr class="form-field">';
            echo '<th scope="row"><label><strong>Şehir Seçimi</strong></label></th>';
            echo '<td><p><strong>Uyarı:</strong> Önce şehir eklemeniz gerekiyor. <a href="' . admin_url('edit.php?post_type=cities') . '">Şehir eklemek için tıklayın</a></p></td>';
            echo '</tr>';
            return;
        }

        // Mevcut seçili şehirleri getir
        $selected_cities = get_term_meta($term->term_id, '_available_cities', true);
        if (!is_array($selected_cities)) {
            $selected_cities = array();
        }

        echo '<tr class="form-field">';
        echo '<th scope="row"><label><strong>Bu kategori hangi şehirlerde gösterilecek?</strong></label></th>';
        echo '<td>';
        echo '<p class="description">Kategorinin hangi şehirlerde görüneceğini seçin. Hiçbiri seçilmezse tüm şehirlerde görünür.</p>';

        foreach ($cities as $city) {
            $checked = in_array($city->ID, $selected_cities) ? 'checked' : '';
            echo '<p>';
            echo '<label><input type="checkbox" name="available_cities[]" value="' . $city->ID . '" ' . $checked . '> ' . esc_html($city->post_title) . '</label>';
            echo '</p>';
        }

        echo '</td>';
        echo '</tr>';
    }

    public function save_category_city_fields($term_id) {
        if (isset($_POST['available_cities'])) {
            $available_cities = array_map('intval', $_POST['available_cities']);
            update_term_meta($term_id, '_available_cities', $available_cities);


        } else {
            // Hiçbiri seçilmemişse boş array kaydet
            update_term_meta($term_id, '_available_cities', array());


        }
    }


}

// Eklentiyi başlat
new GlowessCityEcommerce();

// Helper Functions
if (!function_exists('glowess_get_selected_city')) {
    function glowess_get_selected_city() {
        if (isset($_COOKIE['selected_city_id'])) {
            return intval($_COOKIE['selected_city_id']);
        }
        return false;
    }
}

if (!function_exists('glowess_get_city_products')) {
    function glowess_get_city_products($city_id, $args = array()) {
        $default_args = array(
            'post_type' => 'product',
            'posts_per_page' => 12,
            'post_status' => 'publish',
            'meta_query' => array(
                array(
                    'key' => '_available_cities',
                    'value' => serialize(strval($city_id)),
                    'compare' => 'LIKE'
                )
            )
        );

        $args = wp_parse_args($args, $default_args);
        return new WP_Query($args);
    }
}


if (!function_exists('glowess_get_template')) {
    /**
     * Eklenti template'ini yükle
     * @param string $template_name Template dosya adı
     * @param array $args Template'e gönderilecek değişkenler
     * @param string $template_path Template klasör yolu (opsiyonel)
     */
    function glowess_get_template($template_name, $args = array(), $template_path = '') {
        if ($args && is_array($args)) {
            extract($args);
        }

        $plugin_path = plugin_dir_path(__FILE__) . 'templates/';

        if ($template_path) {
            $template_file = $template_path . $template_name;
        } else {
            $template_file = $plugin_path . $template_name;
        }

        if (file_exists($template_file)) {
            include $template_file;
        } else {
            echo "<!-- Template bulunamadı: {$template_file} -->";
        }
    }
}
?>